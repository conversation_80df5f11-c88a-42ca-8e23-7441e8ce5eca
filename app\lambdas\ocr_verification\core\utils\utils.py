import boto3
import os
import requests
from decimal import Decimal
from json import JSO<PERSON>ncoder
from urllib.parse import urlparse
from datetime import datetime, timezone, timedelta
import time
import json
import fitz
from botocore.exceptions import ClientError
from requests.exceptions import RequestException
from io import BytesIO
from PIL import Image
from pdf2image import convert_from_bytes
from config.config import config
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_error,
    log_frame_debug,
)
from core.constants.constants import S3_IMAGE_PREFIX
from core.enums.enums import ResponseStatus
import json

# Load configuration object from SSM Parameter Store
config_obj = config.ssm_config

logger = get_logger(__name__)

# Initialize AWS service clients
s3_client = boto3.client("s3")
sns_client = boto3.client("sns")
dynamodb_client = boto3.resource("dynamodb")
dynamodb_table_name = dynamodb_client.Table(
    config.ssm_config.get("dynamodb_table_name")
)
dynamodb_record_ttl = config.ssm_config.get("dynamodb_record_ttl")


def load_pdf_with_text(pdf_bytes):
    """
    Extract text content from PDF bytes using PyMuPDF (fitz).

    This function attempts to extract readable text from each page of the PDF.
    If the total extracted text is less than 500 characters, it's likely a
    scanned document that requires OCR processing.

    Args:
        pdf_bytes: Binary content of the PDF file

    Returns:
        dict: Page-by-page text content with keys like 'page_1', 'page_2', etc.
        None: If PDF appears to be scanned (minimal extractable text)
    """
    try:
        start_time = time.time()
        # Open PDF from memory stream
        doc = fitz.open(stream=pdf_bytes)
        page_contents = {}

        # Extract text from each page sequentially
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text("text")
            # Store text content with 1-based page numbering
            page_contents[f"page_{page_num + 1}"] = text or ""
            log_frame_info(
                logger,
                message=f"Page {page_num + 1} extracted text length: {len(text or '')}",
            )

        # Determine if PDF contains sufficient text content
        total_text = sum(len(content.strip()) for content in page_contents.values())
        log_frame_info(
            logger,
            message=f"Total text length across all pages: {total_text} characters",
        )

        # Threshold check: less than 500 chars indicates likely scanned document
        if total_text < 500:
            log_frame_info(
                logger,
                message=f"PDF appears to be scanned or has minimal text (only {total_text} characters found)",
            )
            return None

        log_frame_info(
            logger,
            message=f"Loaded file and text extraction took {time.time() - start_time:.2f} seconds",
        )
        return page_contents
    except Exception as e:
        log_frame_error(
            logger, message="Error loading PDF as text", error=str(e), exc_info=True
        )
        raise e


def download_pdf(url):
    """
    Download PDF file from HTTP or HTTPS URL.

    Args:
        url (str): A URL starting with 'http://' or 'https://'

    Returns:
        bytes: Binary content of the downloaded PDF file

    Raises:
        ValueError: If URL format is invalid
        RequestException: If HTTP request fails
    """
    try:
        start_time = time.time()
        parsed_url = urlparse(url)
        
        # Validate URL scheme
        if parsed_url.scheme not in ["http", "https"]:
            raise ValueError("Invalid URL format. URL must start with http:// or https://")
        
        log_frame_info(logger, message=f"Downloading PDF from {url}")
        
        # Download file from URL
        response = requests.get(url, stream=False, timeout=30)
        response.raise_for_status()  # Raise exception for HTTP errors
        pdf_bytes = response.content
        
        download_time = time.time() - start_time
        log_frame_info(
            logger, message=f"PDF downloaded as bytes in {download_time:.2f} seconds"
        )
        return pdf_bytes
        
    except RequestException as e:
        log_frame_error(
            logger, 
            message="Failed to download PDF from URL", 
            error=str(e), 
            exc_info=True
        )
        raise e
    except Exception as e:
        log_frame_error(
            logger, message="Failed to download PDF", error=str(e), exc_info=True
        )
        raise e


def convert_pdf_to_images(pdf_bytes):
    """
    Convert PDF pages to optimized JPEG images using pdf2image library.

    This function converts each page of the PDF to a 150 DPI JPEG image,
    which provides good quality for OCR processing while managing memory usage.

    Args:
        pdf_bytes: Binary content of the PDF file

    Returns:
        list: List of tuples containing (page_number, PIL.Image) pairs
        None: If conversion fails
    """
    try:
        pdf_conversion_start_time = time.time()
        
        # Set reasonable image pixel limit to prevent memory issues
        Image.MAX_IMAGE_PIXELS = 100_000_000  # Reduced from 300M to 100M
        
        log_frame_info(logger, message="Starting PDF to image conversion with optimized settings")
        
        # First attempt: Convert all pages at once with optimized settings
        try:
            images_list = convert_from_bytes(
                pdf_bytes,
                dpi=150,  # Reduced from 300 to 150 DPI for memory efficiency
                fmt="JPEG",
                thread_count=1,  # Single thread to reduce memory usage
                use_cropbox=True,
                transparent=False,
                grayscale=False,  # Keep color for better OCR
                size=(None, None),  # No size limit, let DPI control quality
            )
            
            # Process images one by one to manage memory
            images = []
            for i, img in enumerate(images_list):
                try:
                    # Convert to RGB mode to ensure compatibility
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Optimize image size if it's too large
                    max_dimension = 4000  # Maximum width/height
                    if img.width > max_dimension or img.height > max_dimension:
                        img.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)
                        log_frame_info(logger, f"Resized page {i+1} to {img.width}x{img.height}")
                    
                    images.append((i + 1, img))
                    
                    # Force garbage collection after each image
                    import gc
                    gc.collect()
                    
                except Exception as e:
                    log_frame_error(logger, f"Error processing page {i+1}: {e}")
                    continue
            
            pdf_conversion_time = time.time() - pdf_conversion_start_time
            log_frame_info(
                logger,
                message=f"Converted PDF to {len(images)} images at DPI=150 in {pdf_conversion_time:.2f} seconds",
            )
            
            if images:
                return images
                
        except MemoryError:
            log_frame_warning(logger, "Memory error during bulk conversion, trying page-by-page approach")
            
        # Fallback: Convert pages one by one if bulk conversion fails
        try:
            import fitz  # PyMuPDF for page count
            doc = fitz.open(stream=pdf_bytes)
            total_pages = len(doc)
            doc.close()
            
            log_frame_info(logger, f"Attempting page-by-page conversion for {total_pages} pages")
            
            images = []
            for page_num in range(total_pages):
                try:
                    # Convert single page
                    page_images = convert_from_bytes(
                        pdf_bytes,
                        dpi=150,
                        fmt="JPEG",
                        thread_count=1,
                        use_cropbox=True,
                        transparent=False,
                        first_page=page_num + 1,
                        last_page=page_num + 1,
                    )
                    
                    if page_images:
                        img = page_images[0]
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        
                        # Optimize image size
                        max_dimension = 4000
                        if img.width > max_dimension or img.height > max_dimension:
                            img.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)
                        
                        images.append((page_num + 1, img))
                        log_frame_info(logger, f"Converted page {page_num + 1}")
                        
                        # Clear memory
                        del page_images
                        import gc
                        gc.collect()
                        
                except Exception as e:
                    log_frame_error(logger, f"Error converting page {page_num + 1}: {e}")
                    continue
            
            pdf_conversion_time = time.time() - pdf_conversion_start_time
            log_frame_info(
                logger,
                message=f"Page-by-page conversion completed: {len(images)} images in {pdf_conversion_time:.2f} seconds",
            )
            
            return images if images else None
            
        except Exception as e:
            log_frame_error(logger, f"Page-by-page conversion also failed: {e}")
            return None
        
    except Exception as e:
        log_frame_error(logger, message=f"Error converting PDF to images: {e}")
        return None


def delete_existing_images_from_s3(bucket_name, request_id):
    """
    Clean up existing images for a request by deleting the entire images directory.

    This function removes all objects under the 'images/' prefix for a given
    request_id to ensure clean storage before uploading new images.

    Args:
        bucket_name (str): S3 bucket name
        request_id (str): Unique identifier for the processing request
    """
    # Build S3 prefix path for this request's images
    prefix = S3_IMAGE_PREFIX.format(request_id=request_id)
    response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)

    # Delete all objects found under the prefix
    if "Contents" in response:
        delete_keys = [{"Key": obj["Key"]} for obj in response["Contents"]]
        s3_client.delete_objects(Bucket=bucket_name, Delete={"Objects": delete_keys})
        log_frame_info(
            logger,
            message=f"Deleted 'images/' directory with {len(delete_keys)} object(s) under {prefix}",
        )
    else:
        log_frame_info(logger, message=f"No objects found under {prefix} to delete.")


def store_images_to_s3(images, request_id):
    """
    Upload converted PDF images to S3 bucket with organized structure.

    This function stores each page image as a separate JPEG file in S3,
    organized under a request-specific directory structure.

    Args:
        images: List of (page_number, PIL.Image) tuples from convert_pdf_to_images
        request_id (str): Unique identifier for the processing request

    Returns:
        list: List of S3 paths where images were stored

    Raises:
        ClientError: If S3 upload fails
        Exception: For other upload-related errors
    """
    try:
        start_time = time.time()
        bucket_name = config_obj["s3_bucket_tracking"]
        s3_paths = []

        # Clean up any existing images for this request
        delete_existing_images_from_s3(bucket_name, request_id)

        log_frame_info(
            logger,
            message=f"Storing {len(images)} images to S3 bucket: {bucket_name} for request_id: {request_id}",
        )

        # Upload each image as a separate JPEG file with memory optimization
        for page_num, image in images:
            try:
                # Convert PIL Image to JPEG bytes with compression
                buffered = BytesIO()
                image.save(buffered, format="JPEG", quality=85, optimize=True)
                image_bytes = buffered.getvalue()
                
                # Clear the buffer immediately to free memory
                buffered.close()

                # Create structured S3 key path: images/{request_id}/page{N}.jpg
                s3_key = (
                    f"{S3_IMAGE_PREFIX.format(request_id=request_id)}page{page_num}.jpg"
                )

                # Upload image to S3 with proper content type
                s3_client.put_object(
                    Bucket=bucket_name,
                    Key=s3_key,
                    Body=image_bytes,
                    ContentType="image/jpeg",
                )

                # Clear image bytes from memory
                del image_bytes

                # Note: This appears to be a hardcoded path that should be dynamic
                s3_paths.append(
                    "s3://claims-verification-requests-tracking-dev/converted_documents"
                )
                log_frame_debug(logger, message=f"Uploaded image for page {page_num} to S3")
                
                # Force garbage collection after each upload
                import gc
                gc.collect()
                
            except Exception as e:
                log_frame_error(logger, f"Failed to upload page {page_num}: {e}")
                continue

        total_time = time.time() - start_time
        log_frame_info(
            logger,
            message=f"Successfully stored {len(images)} images to S3 in {total_time:.2f} seconds",
        )
        return s3_paths

    except ClientError as e:
        error_msg = f"Failed to upload images to S3 bucket {bucket_name}: {str(e)}"
        log_frame_error(logger, message=error_msg, exc_info=True)
        raise
    except Exception as e:
        error_msg = f"Error storing images to S3: {str(e)}"
        log_frame_error(logger, message=error_msg, exc_info=True)
        raise


def fetch_images_from_s3(request_id):
    """
    Retrieve previously stored images from S3 and convert them back to PIL Images.

    This function downloads all images associated with a request_id from S3,
    converts them to PIL Image objects, and returns them sorted by page number.

    Args:
        request_id (str): Unique identifier for the processing request

    Returns:
        list: List of (page_number, PIL.Image) tuples sorted by page number
        dict: Error response with status and message if operation fails
    """
    try:
        start_time = time.time()
        bucket_name = config_obj["s3_bucket_tracking"]
        prefix = S3_IMAGE_PREFIX.format(request_id=request_id)
        images = []

        log_frame_info(
            logger, message=f"Fetching images from S3 for request_id: {request_id}"
        )

        # Use paginator to handle large numbers of objects efficiently
        paginator = s3_client.get_paginator("list_objects_v2")
        page_iterator = paginator.paginate(Bucket=bucket_name, Prefix=prefix)

        # Process each page of results from S3
        for page in page_iterator:
            if "Contents" not in page:
                continue

            # Download and process each image file
            for obj in page["Contents"]:
                s3_key = obj["Key"]
                # Filter for JPEG files with 'page' in filename
                if s3_key.endswith(".jpg") and "page" in s3_key:
                    try:
                        # Extract page number from filename (e.g., page1.jpg -> 1)
                        page_num = int(s3_key.split("page")[1].split(".")[0])

                        # Download image bytes and convert to PIL Image
                        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
                        image_bytes = response["Body"].read()
                        image = Image.open(BytesIO(image_bytes))

                        images.append((page_num, image))
                        log_frame_debug(logger, message=f"Fetched page {page_num}")

                    except (ValueError, Exception) as e:
                        log_frame_error(
                            logger, message=f"Failed to process {s3_key}: {str(e)}"
                        )
                        raise

        # Sort images by page number to maintain document order
        images.sort(key=lambda x: x[0])

        total_time = time.time() - start_time
        log_frame_info(
            logger,
            message=f"Successfully fetched {len(images)} images in {total_time:.2f} seconds",
        )

        if not images:
            log_frame_error(
                logger, message=f"No images found in S3 for request_id: {request_id}"
            )
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"No images found in S3 for request_id: {request_id}",
            }

        return images

    except ClientError as e:
        error_msg = f"Failed to fetch images from S3: {str(e)}"
        log_frame_error(logger, message=error_msg, exc_info=True)
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"Failed to fetch images from S3: {str(e)}",
        }
    except Exception as e:
        error_msg = f"Error fetching images from S3: {str(e)}"
        log_frame_error(logger, message=error_msg, exc_info=True)
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"Error fetching images from S3: {str(e)}",
        }


def is_valid_value(value):
    """
    Check if a value is considered valid (not None, not empty string, or is a number).

    Args:
        value: Any value to validate

    Returns:
        bool: True if value is valid, False otherwise
    """
    if value is None:
        return False
    if isinstance(value, str):
        return value.strip() != ""
    if isinstance(value, (int, float)):
        return True
    return False


class DecimalEncoder(JSONEncoder):
    """
    Custom JSON encoder that converts Decimal objects to float for JSON serialization.
    This is needed because DynamoDB returns Decimal objects that aren't JSON serializable.
    """

    def default(self, o):
        if isinstance(o, Decimal):
            return float(o)  # Convert Decimal to float for JSON compatibility
        return super().default(o)

def get_action_from_ocr_response(response):
    status = response.get("status", "UNKNOWN")

    # Default to REJECT for unknown status
    if status == "SUCCESS":
        # Check for anomalies
        has_anomalies = False
        anomaly_detection = response.get("anomaly_detection")

        if anomaly_detection:
            # If it's a JSON string, parse it
            if isinstance(anomaly_detection, str):
                try:
                    anomaly_detection = json.loads(anomaly_detection)
                except Exception as e:
                    logger.warning(f"Failed to parse anomaly_detection JSON: {str(e)}")
                    # Continue with default has_anomalies=False

            # Check for anomalies in the claims
            if isinstance(anomaly_detection, dict) and "claims_analyzed" in anomaly_detection:
                claims = anomaly_detection.get("claims_analyzed", [])
                anomalies = [c for c in claims if c.get("status") == "ANOMALY"]
                has_anomalies = len(anomalies) > 0

        return "CREATE_CX_TICKET" if has_anomalies else "APPROVE"

    elif status == "UNCLEAR_DOCUMENT" or status == "INVALID_DOCUMENT":
        return "CREATE_CX_TICKET"
        
    elif status == "REJECTED":
        return "REJECT"
        
    else:
        return "CREATE_CX_TICKET"


def publish_to_notification_topic(request_id, response):
    log_frame_debug(
        logger,
        message="Attempting to publish message to notification topic for Request Id",
        request_id=request_id,
    )
    
    # Make a copy to avoid modifying the input response
    message = response.copy() if response else {}
    
    # Ensure request_id is in the message
    message["request_id"] = request_id
    
    # Only add action field for claim_transaction request types
    if message.get("request_type") == "claim_transaction":
        action = get_action_from_ocr_response(message)
        message["action"] = action

    # Get the SNS topic ARN from environment variable
    notification_topic_arn = os.environ.get('NOTIFICATION_TOPIC_ARN')
    if not notification_topic_arn:
        error_msg = "NOTIFICATION_TOPIC_ARN environment variable is not set"
        log_frame_error(logger, error_msg, request_id=request_id)
        raise ValueError(error_msg)

    try:
        sns_response = sns_client.publish(
            TopicArn=notification_topic_arn,
            Message=json.dumps({
                'default': json.dumps(message, cls=DecimalEncoder, ensure_ascii=False)
            }),
            MessageStructure='json',
            MessageAttributes={
                'request_id': {
                    'DataType': 'String',
                    'StringValue': str(request_id)
                },
                'request_type': {
                    'DataType': 'String',
                    'StringValue': str(response.get('request_type', 'unknown'))
                }
            }
        )
        log_frame_info(
            logger,
            message=f"Message published to SNS topic {notification_topic_arn} with message ID: {sns_response['MessageId']}",
            request_id=request_id,
        )
    except Exception as e:
        log_frame_error(
            logger,
            message=f"Failed to publish message to SNS topic {notification_topic_arn} for Request Id: {str(e)}",
            request_id=request_id,
            exc_info=True
        )
        raise Exception(
            f"Failed to publish message to SNS topic {notification_topic_arn} for request {request_id}: {str(e)}"
        )


def get_record_from_dynamodb(request_id):
    """
    Retrieve a record from DynamoDB using the request_id as the primary key.

    Args:
        request_id (str): The unique identifier for the record to retrieve

    Returns:
        dict or None: The DynamoDB item if found, None if not found or on error
    """
    try:
        response = dynamodb_table_name.get_item(Key={"RequestID": request_id})
        return response.get("Item", None)  # Return item if found, else None
    except Exception as e:
        log_frame_error(
            logger, message="Error fetching record from DynamoDB", error=str(e)
        )
        return None


def add_record_to_dynamodb(request_id, data):
    """
    Add a new record or update an existing record in DynamoDB.

    This function merges the request_id with the provided data and stores it
    in DynamoDB. If a record with the same RequestID exists, it will be overwritten.

    Args:
        request_id (str): Unique identifier that serves as the primary key
        data (dict): Additional data fields to store with the record

    Returns:
        dict: DynamoDB response from the put_item operation

    Raises:
        Exception: If the DynamoDB operation fails
    """
    try:
        item = {"RequestID": request_id, **data}
        response = dynamodb_table_name.put_item(Item=item)
        log_frame_info(
            logger,
            message="Successfully added or updated record in DynamoDB for Request Id",
            request_id=request_id,
        )
        return response
    except Exception as e:
        log_frame_error(
            logger,
            message="Error adding or updating record in DynamoDB:",
            error=str(e),
            request_id=request_id,
        )
        raise e


def get_current_timestamp():
    """
    Get the current UTC timestamp in ISO 8601 format.

    """
    return datetime.now(timezone.utc).isoformat()


def get_ttl_timestamp():
    """
    Calculate TTL (Time To Live) timestamp for DynamoDB records.

    This function calculates when a DynamoDB record should expire based on
    the configured TTL hours from the system configuration.

    Returns:
        int: Unix timestamp representing when the record should expire
    """
    ttl_hours = int(dynamodb_record_ttl)
    return int((datetime.now(timezone.utc) + timedelta(hours=ttl_hours)).timestamp())


def get_json_value(json_string: str, key: str):
    """
    Extracts the value of a given key from a JSON string.

    This function safely parses a JSON string and retrieves a specific key's value,
    handling malformed JSON gracefully.

    Args:
        json_string (str): The JSON string containing key-value pairs
        key (str): The key whose value needs to be retrieved

    Returns:
        Any: The value of the key if found, None if key doesn't exist,
             empty dict if JSON is malformed
    """
    try:
        json_data = json.loads(json_string)  # Convert JSON string to dict
        return json_data.get(key, None)  # Return value or None if key not found
    except json.JSONDecodeError:
        return {}  # Return empty dict for malformed JSON


# Extract and convert LLM token pricing configuration from system settings
input_token_cost_per_1k = get_json_value(
    config.ssm_config.get("bedrock_inference_config"), "input_token_cost"
)

output_token_cost_per_1k = get_json_value(
    config.ssm_config.get("bedrock_inference_config"), "output_token_cost"
)

# Convert the token costs to float for mathematical operations
input_token_cost_per_1k = (
    float(input_token_cost_per_1k) if input_token_cost_per_1k else None
)
output_token_cost_per_1k = (
    float(output_token_cost_per_1k) if output_token_cost_per_1k else None
)


def calculate_llm_usage_metrics(current, prior=None):
    """
    Calculate and aggregate LLM usage metrics by combining current and prior usage data.

    This function computes token usage, costs, and latency metrics for LLM operations,
    allowing for cumulative tracking across multiple API calls.

    Args:
        current (dict): Current usage data containing 'llm_usage' metrics
        prior (dict, optional): Previous usage metrics to add to current usage

    Returns:
        dict: Aggregated metrics including:
            - inputTokens: Total input tokens used
            - outputTokens: Total output tokens generated
            - inputTokenCost: Cost for input tokens
            - outputTokenCost: Cost for output tokens
            - totalCost: Combined input and output costs
            - latencyMs: Total latency in milliseconds
    """

    # Initialize metrics structure with default values
    metrics = {
        "inputTokens": 0,
        "outputTokens": 0,
        "inputTokenCost": 0.0,
        "outputTokenCost": 0.0,
        "totalCost": 0.0,
        "latencyMs": 0,
    }

    prior = prior or {}  # Default to empty dict if no prior data

    # Extract current usage metrics from the nested structure
    llm_usage = current.get("llm_usage", {})
    input_tokens = llm_usage.get("inputTokens", 0)
    output_tokens = llm_usage.get("outputTokens", 0)
    latency_ms = llm_usage.get("latencyMs", 0)

    # Aggregate current usage with prior usage
    metrics["inputTokens"] = float(input_tokens) + float(prior.get("inputTokens", 0))
    metrics["outputTokens"] = float(output_tokens) + float(prior.get("outputTokens", 0))
    metrics["latencyMs"] = float(latency_ms) + float(prior.get("latencyMs", 0))

    # Calculate costs only if pricing configuration is available
    if input_token_cost_per_1k and output_token_cost_per_1k:
        metrics["inputTokenCost"] = round(
            (metrics["inputTokens"] / 1000) * float(input_token_cost_per_1k), 4
        )
        metrics["outputTokenCost"] = round(
            (metrics["outputTokens"] / 1000) * float(output_token_cost_per_1k), 4
        )
        metrics["totalCost"] = round(
            metrics["inputTokenCost"] + metrics["outputTokenCost"], 4
        )

    return metrics


def convert_floats_to_decimals(obj):
    """
    Recursively convert all float values in a nested object to Decimal type.

    This function is essential for DynamoDB operations because DynamoDB doesn't
    natively support float types and requires Decimal for numerical precision.

    Args:
        obj: Any object (dict, list, tuple, float, or other type)

    Returns:
        Object with all floats converted to Decimal, maintaining original structure
    """
    if isinstance(obj, float):
        return Decimal(
            str(obj)
        )  # Convert float to Decimal (str prevents floating-point precision issues)
    elif isinstance(obj, dict):
        return {k: convert_floats_to_decimals(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [convert_floats_to_decimals(v) for v in obj]
    else:
        return obj


def extract_anomalies(data: str) -> str:
    """
    Extract and filter only anomalous claims from analysis data.

    This function processes claims analysis results and returns only those
    claims that have been identified as anomalies, along with the overall assessment.
    Supports both legacy format (claim_text, reference_data) and new function calling format
    (claim_field, user_value, verified_value).

    Args:
        data (str): JSON string containing claims analysis with status information

    Returns:
        str: JSON string containing only anomalous claims and overall assessment,
             or error message if input is invalid
    """
    try:
        # Filter claims to include only those marked as anomalies
        anomaly_claims = []

        for claim in data.get("claims_analyzed", []):
            if claim.get("status") == "ANOMALY":
                # Handle both legacy and new function calling formats
                if "claim_text" in claim and "reference_data" in claim:
                    # Legacy format
                    anomaly_claim = {
                        "claim_text": claim["claim_text"],
                        "status": claim["status"],
                        "reference_data": claim["reference_data"],
                        "reasoning": claim["reasoning"],
                    }
                elif "claim_field" in claim and "user_value" in claim and "verified_value" in claim:
                    # New function calling format
                    anomaly_claim = {
                        "claim_field": claim["claim_field"],
                        "user_value": claim["user_value"],
                        "verified_value": claim["verified_value"],
                        "status": claim["status"],
                        "reasoning": claim["reasoning"],
                    }
                    # Include buffer calculation if present
                    if "buffer_calculation" in claim:
                        anomaly_claim["buffer_calculation"] = claim["buffer_calculation"]
                else:
                    # Fallback: include all available fields
                    anomaly_claim = {k: v for k, v in claim.items() if k != "status" or v == "ANOMALY"}

                anomaly_claims.append(anomaly_claim)

        # Build the filtered output structure
        output = {
            "claims_analyzed": anomaly_claims,
            "overall_assessment": data.get("overall_assessment", ""),
        }

        # Convert the result back to a JSON string
        return json.dumps(output, indent=2)

    except (json.JSONDecodeError, KeyError) as e:
        return json.dumps({"error": f"Invalid input or missing fields: {str(e)}"})


def is_empty(value):
    """
    Check if a value is considered empty across multiple data types.

    This function provides a comprehensive emptiness check that handles
    various data types commonly encountered in data processing.

    Args:
        value: Any value to check for emptiness

    Returns:
        bool: True if value is considered empty, False otherwise

    Note:
        - Numbers <= 0 are considered empty
        - Empty collections (list, set, dict) are empty
        - None and empty/whitespace strings are empty
    """
    return (
        value is None
        or (
            isinstance(value, str) and value.strip() == ""
        )  # Empty or whitespace string
        or (
            isinstance(value, (list, set, dict)) and len(value) == 0
        )  # Empty collections
        or (isinstance(value, (int, float)) and value <= 0)  # Non-positive numbers
    )


def save_request_to_dynamodb(
    request_id, request_type, payload, response, processing_time
):
    """
    Save comprehensive request details to DynamoDB for tracking and analytics.

    This function stores request processing information including input payload,
    output response, LLM usage metrics, and processing time history. It maintains
    a running history of processing times for performance analysis.

    Args:
        request_id (str): Unique identifier for the request
        request_type (str): Type/category of the request being processed
        payload (dict): Original input payload from the event
        response (dict): Output response data (same as sent to notification queue)
        processing_time (float): Total processing time for the request in seconds

    Returns:
        dict: Response from DynamoDB put_item operation or None if failed

    Raises:
        Exception: If the DynamoDB save operation fails
    """
    log_frame_debug(
        logger,
        message="Attempting to save request to DynamoDB for Request Id",
        request_id=request_id,
    )

    try:
        # Retrieve existing record to maintain processing time history
        existing_record = get_record_from_dynamodb(request_id)
        processing_time_array = (
            existing_record.get("processing_time", []) if existing_record else []
        )

        # Append new processing time entry with run number for tracking
        processing_time_array.append(
            {
                "run": len(processing_time_array) + 1,  # Incremental run counter
                "time": str(round(processing_time, 2)),  # Processing time in seconds
            }
        )

        # Extract and prepare LLM usage metrics for DynamoDB storage
        llm_usage = response.get("llm_usage", {})
        if llm_usage:
            llm_usage = convert_floats_to_decimals(
                llm_usage
            )  # Convert floats to Decimals

        # Remove llm_usage from response to store it separately in DynamoDB
        if "llm_usage" in response:
            response.pop("llm_usage")

        # Prepare the complete DynamoDB item structure
        item = {
            "request_type": request_type,
            "payload": json.dumps(payload, cls=DecimalEncoder, ensure_ascii=False),
            "output": json.dumps(response, cls=DecimalEncoder, ensure_ascii=False),
            "llm_usage": llm_usage,  # Separate field for LLM metrics
            "processing_time": processing_time_array,  # Historical processing times
            "status": response.get("status", ResponseStatus.FAIL.value),
            "timestamp": get_current_timestamp(),  # When this record was created
            "ttl": get_ttl_timestamp(),  # When this record should expire
        }

        # Save the complete record to DynamoDB
        response = add_record_to_dynamodb(request_id, item)
        log_frame_info(
            logger,
            message="Successfully saved request to DynamoDB for Request Id",
            request_id=request_id,
        )
        return response
    except Exception as e:
        log_frame_error(
            logger,
            message="Failed to save request to DynamoDB for Request Id",
            request_id=request_id,
            error=str(e),
        )
        raise Exception(f"Failed to save request to DynamoDB {request_id}: {str(e)}")
