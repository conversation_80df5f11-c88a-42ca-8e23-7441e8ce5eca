import * as cdk from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as cr from 'aws-cdk-lib/custom-resources';
import { Construct } from 'constructs';

export interface OcrVerificationParametersProps {
  stage: string;
  resourceNameSuffix: string;
  tags?: { [key: string]: string };
  accountId: string;
  region: string;
}

export class OcrVerificationParameters {
  constructor(scope: Construct, id: string, props: OcrVerificationParametersProps) {
    const { stage, resourceNameSuffix, tags = {}, accountId, region } = props;
    const paramPrefix = `/ocr-verification/${stage}`;
    
    // Environment-specific configurations
    const envConfig = {
      development: {
        s3ArtifactsBucket: `ocr-verification-general-artifacts-${resourceNameSuffix}`,
        s3TrackingBucket: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
        dynamodbTable: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
        queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/ocr-verification`,
        bedrockConfig: {
          model: "apac.amazon.nova-pro-v1:0",
          temperature: "0"
        }
      },
      staging: {
        s3ArtifactsBucket: `ocr-verification-general-artifacts-${resourceNameSuffix}`,
        s3TrackingBucket: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
        dynamodbTable: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
        queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/ocr-verification`,
        bedrockConfig: {
          model: "apac.amazon.nova-pro-v1:0",
          temperature: "0"
        }
      },
      production: {
        s3ArtifactsBucket: `ocr-verification-general-artifacts-${resourceNameSuffix}`,
        s3TrackingBucket: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
        dynamodbTable: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
        queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/ocr-verification`,
        bedrockConfig: {
          model: "apac.amazon.nova-pro-v1:0",
          temperature: "0"
        }
      }
    };
    
    // Select the appropriate configuration
    const config = envConfig[stage as keyof typeof envConfig] || envConfig.staging;
    
    // Helper function to create parameter with tags
    const createParameterWithTags = (id: string, options: ssm.StringParameterProps) => {
      const parameter = new ssm.StringParameter(scope, id, options);
      
      // Apply tags to each parameter
      if (tags) {
        Object.entries(tags).forEach(([key, value]) => {
          cdk.Tags.of(parameter).add(key, value);
        });
      }
      
      return parameter;
    };
    
    // Bedrock Region parameter
    createParameterWithTags('BedrockRegionParam', {
      parameterName: `${paramPrefix}/bedrock/region`,
      stringValue: 'ap-southeast-1', //need to revert when quota increases
      description: 'Bedrock region parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // Bedrock inference config parameter
    createParameterWithTags('BedrockInferenceConfigParam', {
      parameterName: `${paramPrefix}/bedrock/inference/config`,
      stringValue: JSON.stringify({
        max_attempts: "2",
        bedrock_model_name: config.bedrockConfig.model,
        model_version: "bedrock-2023-05-31",
        bedrock_temperature: config.bedrockConfig.temperature,
        bedrock_max_tokens: "8192",
        bedrock_top_p: "1",
        bedrock_top_k: "50",
        input_token_cost:"0.0008", 
        output_token_cost: "0.0032"
      }),
      description: 'Bedrock inference config parameter',
      tier: ssm.ParameterTier.STANDARD,
    });

    // Gemini API Key parameter handling
    // Reintroduce the resource with a RETAIN deletion policy and an always-false
    // condition so CloudFormation will retain any existing parameter and remove
    // it from stack management without deleting or overwriting the value.
    const retainGeminiApiKeyCondition = new cdk.CfnCondition(scope, 'RetainGeminiApiKey', {
      expression: cdk.Fn.conditionEquals('1', '2'), // always false
    });

    const geminiApiKeyParam = new ssm.StringParameter(scope, 'GeminiApiKeyParam', {
      parameterName: `${paramPrefix}/gemini/api/key`,
      // This placeholder will never be applied because the condition is false.
      // The resource will be retained and removed from stack management.
      stringValue: 'MANAGED_OUTSIDE_CDK',
      description: 'Gemini API key for LLM operations (managed outside CDK)',
      tier: ssm.ParameterTier.STANDARD,
    });
    const geminiApiKeyCfn = geminiApiKeyParam.node.defaultChild as ssm.CfnParameter;
    geminiApiKeyCfn.cfnOptions.condition = retainGeminiApiKeyCondition;
    geminiApiKeyParam.applyRemovalPolicy(cdk.RemovalPolicy.RETAIN);

    // Gemini inference config parameter
    createParameterWithTags('GeminiInferenceConfigParam', {
      parameterName: `${paramPrefix}/gemini/inference/config`,
      stringValue: JSON.stringify({
        gemini_model_name: "gemini-2.5-flash",
        max_output_tokens: 2000,
        temperature: 0.0,
        top_p: 0.95,
        candidate_count: 1,
        max_attempts: 2
      }),
      description: 'Gemini inference configuration parameters',
      tier: ssm.ParameterTier.STANDARD,
    });

    // LLM Provider selector parameter
    createParameterWithTags('LlmProviderParam', {
      parameterName: `${paramPrefix}/llm/provider`,
      stringValue: 'gemini', // Default to gemini, can be switched to 'bedrock'
      description: 'LLM provider selection (bedrock or gemini)',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket parameter for artifacts
    createParameterWithTags('S3ArtifactsBucketParam', {
      parameterName: `${paramPrefix}/s3/artifacts`,
      stringValue: config.s3ArtifactsBucket,
      description: 'S3 bucket parameter for artifacts',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket prompts directory parameter
    createParameterWithTags('S3ArtifactsPromptsDirParam', {
      parameterName: `${paramPrefix}/s3/artifacts/prompts`,
      stringValue: 'prompts/',
      description: 'S3 bucket prompts directory parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket for tracking requests parameter
    createParameterWithTags('S3TrackingBucketParam', {
      parameterName: `${paramPrefix}/s3/tracking`,
      stringValue: config.s3TrackingBucket,
      description: 'S3 bucket for tracking requests parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // SQS notification queue parameter
    createParameterWithTags('SQSNotificationQueueParam', {
      parameterName: `${paramPrefix}/queue/notification`,
      stringValue: `${config.queueUrlBase}-notification-queue-${resourceNameSuffix}`,
      description: 'SQS notification queue parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // Dynamodb table parameter
    createParameterWithTags('DynamoDBTableParam', {
      parameterName: `${paramPrefix}/dynamodb/table`,
      stringValue: config.dynamodbTable,
      description: 'Dynamodb table parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // Dynamodb ttl parameter
    createParameterWithTags('DynamoDBTtlParam', {
      parameterName: `${paramPrefix}/dynamodb/ttl`,
      stringValue: '48',
      description: 'Dynamodb ttl parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // LLM images process limit
    createParameterWithTags('ImagesLimitParam', {
      parameterName: `${paramPrefix}/bedrock/maximage/size`,
      stringValue: '13',
      description: 'LLM images process limit',
      tier: ssm.ParameterTier.STANDARD,
    });

    // TEMPORARY: Document quality parameters (force create or update with overwrite)
    // Use custom resources to call SSM PutParameter with Overwrite=true to avoid AlreadyExists errors
    // when parameters were created outside of this stack. This will create if missing and update if present.

    // Prepare names for the SSM parameters
    const thresholdParamName = `${paramPrefix}/document-quality/score/threshold`;
    const weightsParamName = `${paramPrefix}/document-quality/score/weights`;

    const thresholdArn = `arn:aws:ssm:${region}:${accountId}:parameter${thresholdParamName}`;
    const weightsArn = `arn:aws:ssm:${region}:${accountId}:parameter${weightsParamName}`;

    const thresholdParam = new cr.AwsCustomResource(scope, 'DocumentQualityThresholdParam', {
      onCreate: {
        service: 'SSM',
        action: 'putParameter',
        parameters: {
          Name: thresholdParamName,
          Type: 'String',
          Value: '0.5',
          Overwrite: true,
          Description: 'Document quality threshold score',
          Tier: 'Standard',
        },
        physicalResourceId: cr.PhysicalResourceId.of(thresholdParamName),
      },
      onUpdate: {
        service: 'SSM',
        action: 'putParameter',
        parameters: {
          Name: thresholdParamName,
          Type: 'String',
          Value: '0.5',
          Overwrite: true,
          Description: 'Document quality threshold score',
          Tier: 'Standard',
        },
        physicalResourceId: cr.PhysicalResourceId.of(thresholdParamName),
      },
      policy: cr.AwsCustomResourcePolicy.fromSdkCalls({
        resources: [thresholdArn],
      }),
    });

    const weightsParamValue = JSON.stringify({
      text_clarity: 0.4,
      contrast_readability: 0.2,
      resolution_text_size: 0.3,
      overall_image_quality: 0.1,
    });

    const weightsParam = new cr.AwsCustomResource(scope, 'DocumentQualityWeightsParam', {
      onCreate: {
        service: 'SSM',
        action: 'putParameter',
        parameters: {
          Name: weightsParamName,
          Type: 'String',
          Value: weightsParamValue,
          Overwrite: true,
          Description: 'Document Quality weights for each category',
          Tier: 'Standard',
        },
        physicalResourceId: cr.PhysicalResourceId.of(weightsParamName),
      },
      onUpdate: {
        service: 'SSM',
        action: 'putParameter',
        parameters: {
          Name: weightsParamName,
          Type: 'String',
          Value: weightsParamValue,
          Overwrite: true,
          Description: 'Document Quality weights for each category',
          Tier: 'Standard',
        },
        physicalResourceId: cr.PhysicalResourceId.of(weightsParamName),
      },
      policy: cr.AwsCustomResourcePolicy.fromSdkCalls({
        resources: [weightsArn],
      }),
    });



    // OCR service configuration parameters
    createParameterWithTags('OcrServiceUrl', {
      parameterName: `${paramPrefix}/ocr/service/url`,
      stringValue: 'https://42vwqwgpsjdwehfuc6rpmzlvj40loxhw.lambda-url.us-east-1.on.aws/',
      description: 'Lambda OCR service URL for document processing',
      tier: ssm.ParameterTier.STANDARD,
    });

    createParameterWithTags('OcrConfidenceThreshold', {
      parameterName: `${paramPrefix}/ocr/confidence/threshold`,
      stringValue: '80',
      description: 'OCR confidence threshold for document quality assessment',
      tier: ssm.ParameterTier.STANDARD,
    });

    createParameterWithTags('OcrPdfTimeout', {
      parameterName: `${paramPrefix}/ocr/pdf/timeout`,
      stringValue: '300',
      description: 'OCR service timeout for PDF processing (seconds)',
      tier: ssm.ParameterTier.STANDARD,
    });

    createParameterWithTags('OcrImageTimeout', {
      parameterName: `${paramPrefix}/ocr/image/timeout`,
      stringValue: '120',
      description: 'OCR service timeout for image processing (seconds)',
      tier: ssm.ParameterTier.STANDARD,
    });

    createParameterWithTags('OcrMaxWorkers', {
      parameterName: `${paramPrefix}/ocr/max/workers`,
      stringValue: '8',
      description: 'Maximum concurrent workers for OCR parallel processing',
      tier: ssm.ParameterTier.STANDARD,
    });



    // Supported Document Types parameter
    createParameterWithTags('SupportedDocumentTypes', {
      parameterName: `${paramPrefix}/supported/document_types`,
      stringValue: "EJARI,FORM-F",
      description: 'Supported Document Types',
      tier: ssm.ParameterTier.STANDARD,
    });
  }
}
