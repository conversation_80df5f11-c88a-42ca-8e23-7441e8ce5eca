import requests
import json
import time
import base64
import math
import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from io import BytesIO
from PIL import Image
from pdf2image import convert_from_bytes
from config.config import config
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_error,
    log_frame_debug,
    log_frame_warning,
)
from core.enums.enums import ResponseStatus

# Initialize logger
logger = get_logger(__name__)

# Load configuration
config_obj = config.ssm_config

# Default OCR service configuration
DEFAULT_OCR_SERVICE_URL = "https://42vwqwgpsjdwehfuc6rpmzlvj40loxhw.lambda-url.us-east-1.on.aws/"
DEFAULT_REQUEST_TIMEOUT = 300  # 5 minutes
DEFAULT_IMAGE_REQUEST_TIMEOUT = 120  # 2 minutes
LAMBDA_PAYLOAD_LIMIT_BYTES = 6 * 1024 * 1024  # 6MB AWS Lambda Function URL limit
DEFAULT_MAX_WORKERS = 8


def get_ocr_service_config():
    """
    Get OCR service configuration from SSM or use defaults.
    
    Returns:
        dict: OCR service configuration
    """
    # Add detailed logging for SSM parameter reading
    ocr_confidence_threshold = config_obj.get("ocr_confidence_threshold")
    
    log_frame_info(logger, f"SSM Parameter Reading - ocr_confidence_threshold: {ocr_confidence_threshold}")
    
    # Use OCR confidence threshold if available, otherwise use default
    if ocr_confidence_threshold is not None:
        confidence_threshold = float(ocr_confidence_threshold)
        log_frame_info(logger, f"Using OCR confidence threshold: {confidence_threshold}")
    else:
        confidence_threshold = 60.0
        log_frame_info(logger, f"Using default confidence threshold: {confidence_threshold}")
    
    config = {
        "base_url": config_obj.get("ocr_service_url", DEFAULT_OCR_SERVICE_URL),
        "pdf_timeout": int(config_obj.get("ocr_pdf_timeout", DEFAULT_REQUEST_TIMEOUT)),
        "image_timeout": int(config_obj.get("ocr_image_timeout", DEFAULT_IMAGE_REQUEST_TIMEOUT)),
        "max_workers": int(config_obj.get("ocr_max_workers", DEFAULT_MAX_WORKERS)),
        "confidence_threshold": confidence_threshold
    }
    
    log_frame_info(logger, f"Final OCR service config: {config}")
    return config


def convert_image_to_base64(image_bytes):
    """Convert image bytes to base64 string."""
    return base64.b64encode(image_bytes).decode('utf-8')


def call_ocr_image_api(image_bytes, config):
    """
    Call the OCR service /ocr/image endpoint with image data.
    If image is too large (>6MB), resize it before sending.
    
    Args:
        image_bytes (bytes): Image data
        config (dict): OCR service configuration
        
    Returns:
        dict: OCR result with confidence scores and detections
    """
    try:
        image_size = len(image_bytes)
        log_frame_info(logger, f"Processing image with OCR service (size: {image_size} bytes)")
        
        # Convert to base64 and check actual payload size
        img_base64 = convert_image_to_base64(image_bytes)
        payload = {"image_base64": img_base64}
        payload_json = json.dumps(payload)
        payload_size = len(payload_json.encode('utf-8'))
        
        log_frame_info(logger, f"Payload size: {payload_size} bytes (base64 encoded + JSON wrapper)")
        
        # Check if payload size exceeds Lambda Function URL limit
        if payload_size > LAMBDA_PAYLOAD_LIMIT_BYTES:
            log_frame_info(logger, f"Payload size ({payload_size} bytes) exceeds Lambda limit ({LAMBDA_PAYLOAD_LIMIT_BYTES} bytes). Resizing image.")
            
            # Convert to PIL Image for resizing
            image = Image.open(BytesIO(image_bytes))
            
            # Calculate resize factor to get under 6MB (accounting for base64 encoding overhead ~33%)
            # Base64 encoding increases size by ~33%, so we need to target ~4.5MB for original image
            target_image_size = int(LAMBDA_PAYLOAD_LIMIT_BYTES / 1.4)  # Account for base64 + JSON overhead
            resize_factor = (target_image_size / image_size) ** 0.5 * 0.9  # 90% of calculated size for safety
            
            new_width = int(image.width * resize_factor)
            new_height = int(image.height * resize_factor)
            
            # Ensure minimum size
            if new_width < 100 or new_height < 100:
                log_frame_warning(logger, f"Resized image would be too small ({new_width}x{new_height}). Using minimum size.")
                new_width = max(100, new_width)
                new_height = max(100, new_height)
            
            # Resize image
            resized_image = image.resize((new_width, new_height), Image.LANCZOS)
            resized_bytes = convert_image_to_bytes(resized_image)
            
            # Recalculate payload size with resized image
            img_base64 = convert_image_to_base64(resized_bytes)
            payload = {"image_base64": img_base64}
            payload_json = json.dumps(payload)
            payload_size = len(payload_json.encode('utf-8'))
            
            log_frame_info(logger, f"Resized image from {image.width}x{image.height} to {new_width}x{new_height} (size: {len(resized_bytes)} bytes, payload: {payload_size} bytes)")
            image_bytes = resized_bytes
        
        image_url = f"{config['base_url'].rstrip('/')}/ocr/image"

        log_frame_info(logger, f"Calling OCR image API (payload size: {payload_size} bytes)")
        response = requests.post(image_url, json=payload, timeout=config['image_timeout'])
        
        if response.status_code == 200:
            result = response.json()
            log_frame_info(logger, f"OCR image API successful, got {len(result.get('detections', []))} detections")
            return result
        else:
            raise RuntimeError(f"OCR image API failed – status {response.status_code}: {response.text}")
            
    except Exception as e:
        log_frame_error(logger, f"Error calling OCR image API: {str(e)}")
        raise e


def convert_image_to_bytes(image):
    """Convert PIL Image to bytes for API transmission."""
    buffered = BytesIO()
    image.save(buffered, format="PNG")
    return buffered.getvalue()


def send_single_image_to_ocr(page_num, image, config):
    """
    Send one page image to /ocr/image and return JSON result with page tags.
    
    Args:
        page_num (int): Page number
        image (PIL.Image): Image to process
        config (dict): OCR service configuration
        
    Returns:
        dict: OCR result with confidence scores and detections
    """
    try:
        image_url = f"{config['base_url'].rstrip('/')}/ocr/image"
        img_bytes = convert_image_to_bytes(image)
        img_base64 = convert_image_to_base64(img_bytes)
        payload = {"image_base64": img_base64}
        payload_json = json.dumps(payload)
        payload_size = len(payload_json.encode('utf-8'))

        log_frame_info(logger, f"Processing page {page_num} with OCR service (image: {len(img_bytes)} bytes, payload: {payload_size} bytes)")
        response = requests.post(image_url, json=payload, timeout=config['image_timeout'])
        
        if response.status_code == 200:
            result = response.json()
            
            # Add page numbers to detections
            for det in result.get('detections', []):
                det['page'] = page_num
                
            # Ensure page statistics have page numbers
            if result.get('page_statistics'):
                for ps in result['page_statistics']:
                    ps['page_number'] = page_num
                    
            log_frame_info(logger, f"Page {page_num} OCR successful, got {len(result.get('detections', []))} detections")
            return result
        else:
            raise RuntimeError(f"Page {page_num} failed – status {response.status_code}: {response.text}")
            
    except Exception as e:
        log_frame_error(logger, f"Error processing page {page_num}: {str(e)}")
        raise e


def process_pdf_as_images_parallel(pdf_bytes, config):
    """
    Convert PDF to images and process them in parallel.
    
    Args:
        pdf_bytes (bytes): PDF file content
        config (dict): OCR service configuration
        
    Returns:
        dict: Combined OCR processing result
    """
    try:
        log_frame_info(logger, "Converting PDF to images for parallel processing")
        
        # Convert PDF to images
        images = convert_from_bytes(pdf_bytes, dpi=200)
        page_count = len(images)
        
        log_frame_info(logger, f"Converted PDF to {page_count} pages")
        
        # Process images in parallel
        start_time = time.time()
        all_detections = []
        all_page_stats = []
        total_processing_time = 0
        
        max_workers = min(config['max_workers'], os.cpu_count() or 4, len(images))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(send_single_image_to_ocr, page_num, img, config): page_num 
                for page_num, img in enumerate(images, 1)
            }
            
            for future in as_completed(futures):
                page_num = futures[future]
                try:
                    result = future.result()
                    all_detections.extend(result.get('detections', []))
                    if result.get('page_statistics'):
                        all_page_stats.extend(result['page_statistics'])
                    total_processing_time += result.get('processing_time', 0)
                except Exception as e:
                    log_frame_error(logger, f"Failed to process page {page_num}: {str(e)}")
                    raise e
        
        # Sort detections by page and position
        all_detections.sort(key=lambda d: (d.get('page', 0), d.get('bbox', [[0,0]])[0][1]))
        
        # Create final combined result
        full_request_time = time.time() - start_time
        
        final_result = {
            "success": True,
            "detections": all_detections,
            "total_detections": len(all_detections),
            "processing_time": total_processing_time,
            "page_count": page_count,
            "page_statistics": sorted(all_page_stats, key=lambda x: x['page_number']) if all_page_stats else None
        }
        
        log_frame_info(logger, f"All pages processed successfully in {full_request_time:.2f}s")
        return final_result
        
    except Exception as e:
        log_frame_error(logger, f"Error processing PDF as images: {str(e)}")
        raise e


def call_ocr_pdf_api(pdf_bytes, config):
    """
    Call the OCR service /ocr/pdf endpoint with PDF data.
    If PDF is too large (>6MB), convert to images and process in parallel.
    
    Args:
        pdf_bytes (bytes): PDF file content
        config (dict): OCR service configuration
        
    Returns:
        dict: OCR processing result
    """
    try:
        pdf_size = len(pdf_bytes)
        log_frame_info(logger, f"Processing PDF with OCR service (size: {pdf_size} bytes)")
        
        # Check if PDF size exceeds Lambda Function URL limit
        if pdf_size > LAMBDA_PAYLOAD_LIMIT_BYTES:
            log_frame_info(logger, f"PDF size ({pdf_size} bytes) exceeds Lambda limit ({LAMBDA_PAYLOAD_LIMIT_BYTES} bytes). Processing as images in parallel.")
            return process_pdf_as_images_parallel(pdf_bytes, config)
        else:
            log_frame_info(logger, f"PDF size ({pdf_size} bytes) within Lambda limit. Processing as single file.")
            pdf_url = f"{config['base_url'].rstrip('/')}/ocr/pdf"
            
            files = {"file": ("document.pdf", pdf_bytes, "application/pdf")}
            
            start_time = time.time()
            response = requests.post(pdf_url, files=files, timeout=config['pdf_timeout'])
            request_time = time.time() - start_time
            
            log_frame_info(logger, f"OCR PDF API response status: {response.status_code}, time: {request_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                log_frame_info(logger, f"OCR PDF API successful, got {len(result.get('detections', []))} detections")
                return result
            else:
                raise RuntimeError(f"OCR PDF API failed: status {response.status_code}: {response.text}")
                
    except Exception as e:
        log_frame_error(logger, f"Error calling OCR PDF API: {str(e)}")
        raise e


def calculate_confidence_metrics(confidences):
    """
    Calculate various confidence metrics from a list of confidence scores.
    
    Args:
        confidences (list): List of confidence scores (0.0-1.0)
        
    Returns:
        dict: Dictionary containing various confidence metrics
    """
    if not confidences:
        return {
            "geometric_mean": 0.0,
            "arithmetic_mean": 0.0,
            "harmonic_mean": 0.0,
            "minimum": 0.0,
            "maximum": 0.0,
            "median": 0.0,
            "std_deviation": 0.0,
            "total_words": 0
        }
    
    n = len(confidences)
    
    # Convert to percentages for easier interpretation
    confidences_percent = [conf * 100.0 if conf <= 1.0 else conf for conf in confidences]
    
    # Arithmetic mean (simple average)
    arithmetic_mean = sum(confidences_percent) / n
    
    # Geometric mean (best for combining probabilities)
    # Convert to 0-1 range if needed
    confidence_ratios = [conf / 100.0 if conf > 1.0 else conf for conf in confidences]
    product = 1.0
    for conf in confidence_ratios:
        product *= max(conf, 0.001)  # Avoid zero values
    geometric_mean = (product ** (1.0 / n)) * 100.0
    
    # Harmonic mean (more conservative)
    harmonic_sum = sum(1.0 / max(conf, 0.001) for conf in confidences_percent)
    harmonic_mean = n / harmonic_sum if harmonic_sum > 0 else 0.0
    
    # Min, max, median
    sorted_confs = sorted(confidences_percent)
    minimum = min(confidences_percent)
    maximum = max(confidences_percent)
    median = sorted_confs[n // 2] if n % 2 == 1 else (sorted_confs[n // 2 - 1] + sorted_confs[n // 2]) / 2
    
    # Standard deviation
    variance = sum((conf - arithmetic_mean) ** 2 for conf in confidences_percent) / n
    std_deviation = math.sqrt(variance)
    
    return {
        "geometric_mean": round(geometric_mean, 2),
        "arithmetic_mean": round(arithmetic_mean, 2),
        "harmonic_mean": round(harmonic_mean, 2),
        "minimum": round(minimum, 2),
        "maximum": round(maximum, 2),
        "median": round(median, 2),
        "std_deviation": round(std_deviation, 2),
        "total_words": n
    }


def extract_confidence_scores_from_ocr_result(ocr_result):
    """
    Extract confidence scores from OCR service result.
    
    Args:
        ocr_result (dict): OCR service response
        
    Returns:
        tuple: (confidences_list, words_data_list)
    """
    confidences = []
    words_data = []
    
    for detection in ocr_result.get('detections', []):
        confidence = detection.get('confidence', 0)
        text = detection.get('text', '').strip()
        bbox = detection.get('bbox', [])
        
        if text and len(text) > 0:  # Only include actual text detections
            confidences.append(confidence / 100.0 if confidence > 1.0 else confidence)
            
            # Convert bbox format if needed (handle different formats)
            if bbox and len(bbox) >= 4:
                if isinstance(bbox[0], list):  # [[x1,y1], [x2,y2], ...] format
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    left = min(x_coords) / 1000.0  # Normalize to 0-1 range
                    top = min(y_coords) / 1000.0
                    width = (max(x_coords) - min(x_coords)) / 1000.0
                    height = (max(y_coords) - min(y_coords)) / 1000.0
                else:  # [x, y, width, height] format
                    left, top, width, height = bbox[:4]
                    if left > 1 or top > 1:  # Denormalize if needed
                        left = left / 1000.0
                        top = top / 1000.0
                        width = width / 1000.0
                        height = height / 1000.0
            else:
                left = top = width = height = 0
                
            words_data.append({
                "text": text,
                "confidence": confidence,
                "bbox": {
                    "left": left,
                    "top": top,
                    "width": width,
                    "height": height
                }
            })
    
    return confidences, words_data


def assess_document_quality_with_lambda_ocr(image_bytes):
    """
    Assess document quality using Lambda OCR service confidence scores.
    
    Args:
        image_bytes: Image data to assess
        
    Returns:
        dict: Quality assessment results with confidence scores and routing decision
    """
    try:
        start_time = time.time()
        config = get_ocr_service_config()
        
        log_frame_info(logger, f"Processing image with Lambda OCR service (size: {len(image_bytes)} bytes)")
        
        # Call OCR service API
        ocr_result = call_ocr_image_api(image_bytes, config)
        
        processing_time = time.time() - start_time
        log_frame_info(logger, f"Lambda OCR processing completed in {processing_time:.2f} seconds")
        
        # Extract confidence scores from OCR result
        confidences, words_data = extract_confidence_scores_from_ocr_result(ocr_result)
        
        # Calculate confidence metrics
        confidence_metrics = calculate_confidence_metrics(confidences)
        
        # Determine quality assessment based on confidence scores
        primary_confidence = confidence_metrics['geometric_mean']
        
        # Quality assessment logic based on confidence scores
        if primary_confidence >= 90:
            quality_level = "excellent"
        elif primary_confidence >= 80:
            quality_level = "good"
        elif primary_confidence >= 70:
            quality_level = "fair"
        elif primary_confidence >= 60:
            quality_level = "poor"
        else:
            quality_level = "very_poor"
        
        # Routing decision based on confidence threshold
        confidence_threshold = config['confidence_threshold']
        quality_acceptable = primary_confidence >= confidence_threshold
        
        assessment_summary = (
            f"Lambda OCR analysis: {primary_confidence:.1f}% geometric mean confidence. "
            f"Quality level: {quality_level}. "
            f"Total words detected: {confidence_metrics['total_words']}. "
            f"Confidence range: {confidence_metrics['minimum']:.1f}%-{confidence_metrics['maximum']:.1f}%"
        )
        
        if not quality_acceptable:
            assessment_summary += f" Document quality below threshold ({confidence_threshold}%)."
        
        result = {
            "status": ResponseStatus.SUCCESS.value,
            "result": {
                "rapid_confidence": {  # Updated name for clarity
                    "geometric_mean": primary_confidence,
                    "total_words": confidence_metrics['total_words']
                },
                "quality_assessment": {
                    "quality_level": quality_level,
                    "quality_acceptable": quality_acceptable,
                    "confidence_threshold": confidence_threshold,
                    "assessment_summary": assessment_summary
                },
                "words_data": words_data[:100],  # Limit to first 100 words for performance
                "ocr_service_result": ocr_result  # Include raw OCR result for debugging
            },
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": int(processing_time * 1000),
            },
        }
        
        log_frame_info(logger, f"Quality assessment completed: {quality_level} ({primary_confidence:.1f}% confidence)")
        return result
        
    except Exception as e:
        log_frame_error(logger, f"Error in Lambda OCR quality assessment: {str(e)}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"Lambda OCR quality assessment failed: {str(e)}",
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": 0,
            },
        }


def assess_pdf_quality_with_lambda_ocr(pdf_bytes):
    """
    Assess PDF document quality using Lambda OCR service.
    
    Args:
        pdf_bytes: PDF document bytes
        
    Returns:
        dict: Quality assessment results for the entire PDF
    """
    try:
        start_time = time.time()
        config = get_ocr_service_config()
        
        log_frame_info(logger, f"Processing PDF with Lambda OCR service (size: {len(pdf_bytes)} bytes)")
        
        # Call OCR service API for PDF
        ocr_result = call_ocr_pdf_api(pdf_bytes, config)
        
        total_time = time.time() - start_time
        
        # Extract confidence scores from all pages
        confidences, words_data = extract_confidence_scores_from_ocr_result(ocr_result)
        
        # Calculate overall confidence metrics
        overall_confidence_metrics = calculate_confidence_metrics(confidences)
        primary_confidence = overall_confidence_metrics['geometric_mean']
        
        # Determine overall quality
        if primary_confidence >= 90:
            quality_level = "excellent"
        elif primary_confidence >= 80:
            quality_level = "good"
        elif primary_confidence >= 70:
            quality_level = "fair"
        elif primary_confidence >= 60:
            quality_level = "poor"
        else:
            quality_level = "very_poor"
        
        # Routing decision
        confidence_threshold = config['confidence_threshold']
        quality_acceptable = primary_confidence >= confidence_threshold
        
        # Create page assessments from page statistics if available
        page_assessments = []
        if ocr_result.get('page_statistics'):
            for page_stat in ocr_result['page_statistics']:
                page_confidence = page_stat.get('arithmetic_mean_confidence', primary_confidence)
                page_assessments.append({
                    "page": page_stat.get('page_number', 1),
                    "confidence": page_confidence,
                    "threshold": confidence_threshold,
                    "accepted": page_confidence >= confidence_threshold
                })
        else:
            # Create single page assessment
            page_assessments.append({
                "page": 1,
                "confidence": primary_confidence,
                "threshold": confidence_threshold,
                "accepted": quality_acceptable
            })
        
        page_count = ocr_result.get('page_count', len(page_assessments))
        total_words = overall_confidence_metrics['total_words']
        
        assessment_summary = (
            f"PDF Lambda OCR analysis: {primary_confidence:.1f}% overall geometric mean confidence. "
            f"Quality level: {quality_level}. "
            f"Total pages: {page_count}, Total words: {total_words}. "
            f"Confidence range: {overall_confidence_metrics['minimum']:.1f}%-{overall_confidence_metrics['maximum']:.1f}%"
        )
        
        if not quality_acceptable:
            assessment_summary += f" Document quality below threshold ({confidence_threshold}%)."
        
        result = {
            "status": ResponseStatus.SUCCESS.value,
            "result": {
                "rapid_confidence": {  # Updated name for clarity
                    "geometric_mean": primary_confidence,
                    "total_words": overall_confidence_metrics['total_words']
                },
                "quality_assessment": {
                    "quality_level": quality_level,
                    "quality_acceptable": quality_acceptable,
                    "confidence_threshold": confidence_threshold,
                    "assessment_summary": assessment_summary
                },
                "page_assessments": page_assessments,
                "ocr_service_result": ocr_result  # Include raw OCR result for debugging
            },
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": int(total_time * 1000),
            },
        }
        
        log_frame_info(logger, f"PDF quality assessment completed: {quality_level} ({primary_confidence:.1f}% confidence)")
        return result
        
    except Exception as e:
        log_frame_error(logger, f"Error in PDF Lambda OCR quality assessment: {str(e)}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"PDF Lambda OCR quality assessment failed: {str(e)}",
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": 0,
            },
        }


# OCR service functions are now the primary implementation
# No backward compatibility aliases needed since Textract is deprecated
